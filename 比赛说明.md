二、赛题与数据（网站发布）
1.	竞赛题目
本赛题聚焦于远洋干散货船，基于船舶历史交易数据、详细技术参数及实时市场环境指标，要求参赛者构建智能估值模型，对指定船舶在目标时间点的市场价值进行精准预测，建立能够反映航运市场特性的估值体系。赛事提供经脱敏处理的全球远洋干散货船舶交易数据集，包含近三年交易流水（其中1500条训练数据及200条测试数据）及对应船舶技术档案与配套市场指数。
2.	竞赛数据（数据集大小评估）
提供1700条远洋干散货船舶交易数据及对应船舶技术档案与配套市场指数，详情如下
交易数据示例：
参数名称	参数说明	参数示例
shipno	船舶ID	ba041d17
sale_date	交易日期	2023-07
saleprice	交易价格	13

档案数据示例：
参数名称	参数说明	参数示例
shipno	船舶ID	ba041d17
gt	总吨	33000
dwt	载重吨	57000
length	船长	1900
width	船宽	320
draught	吃水	130
height	船高	180
speed	服务航区	14
nt	净吨	19000
lbp	垂线间长	1800
freeboard	干舷	100
number_of_holds	货舱数	5
grain_capacity	散粮装载能力	72000
ldt	轻排量吨位	11000
bale_capacity	包装容量	68000
mainenginebore	主机缸径	500
mainenginenumberofcylinders	主机气缸数	6
mainenginerpm	主机转速	120
totalhorsepowerofmainengines	主机总马力	14000
totalkilowattsofmainengines	主机总千瓦	10000
totalbunkercapacity	燃料舱总容量	2400
number_of_all_engines	所有发动机数量	4
power_bhp_ihp_shp_max	船舶引擎或发电机的最大输出功率	14000
power_kw_max	船舶设备的最大输出功率	10000
total_power_of_all_engines	所有发动机的总功率	12000
built_date	船舶建造年份	2010
keellaiddate	龙骨安放年份	2007
contract_date	船舶建造合同年份	2007
flag	船旗	flag_4
register	船籍港	register_3
class	船级社	class_1
ship_status	船舶状态	ship_status_0
ship_design	船舶的设计型号	ship_design_41
main_engine_designer	主机设计者	main_engine_designer_4
main_engine_designer_group	主机设计组	main_engine_designer_group_2
hull_type	船体类型	hull_type_0
boilermaker	锅炉制造商	boilermaker_0
main_engine_builder	主机制造厂	main_engine_builder_2
mainenginemodel	主机型号	mainenginemodel_4
mainenginestroketype	主机冲程类型	mainenginestroketype_0
propellertype	螺旋桨类型	propellertype_0
club	保赔协会代码	club_7
propulsion_type	推进系统类型代码	propulsion_type_0
country_of_build	船厂所属国	country_1
doc_country_of_control	doc公司控制国	country_24
doc_company_country_of_domicile	doc公司登记国	country_21
doc_country_of_registration	doc公司注册国	country_21
group_beneficial_owner_country_of_control	实际受益所有人控制国	country_24
group_beneficial_owner_country_of_domicile	实际受益所有人居籍国	country_24
group_beneficial_owner_country_of_registration	实际受益所有人注册国	country_24
operator_country_of_control	运营公司控制国	country_24
operator_country_of_domicile_name	运营公司所在国	country_21
operator_country_of_registration	运营公司注册国	country_21
registered_owner_country_of_control	注册所有人控制国	country_24
registered_owner_country_of_domicile	注册所有人居籍国	country_11
registered_owner_country_of_registration	注册所有人注册国	country_11
ship_manager_country_of_control	船舶管理公司控制国	country_24
ship_manager_country_of_domicile_name	船舶管理公司居籍国	country_21
ship_manager_country_of_registration	船舶管理公司注册国	country_21
technical_manager_country_of_control	技术管理公司控制国	country_24
technical_manager_country_of_domicile	技术管理公司居籍国	country_21
technical_manager_country_of_registration	技术管理公司注册国	country_21

市场指数数据示例：
参数名称	参数说明	参数示例
date	时间	2022-01
comfactor	船舶综合指数	964.1
dryfactor	干散货船综合指数	973.62

3.提交说明
本次大赛要求选手通过使用主办方提供的数据，训练算法来预测测试集中的干散货船对应时间的交易价格，选手需提交名为result.csv的文件。文件包含两列，分别为序号与预测的交易价格，如下所示。
sequence	saleprice
0	10
1	10
2	10

4.评估指标
选手提交结果与实际交易价格进行对比，以绝对百分比误差为评价指标，结果越小越好。
5.审核提交
评测形式为结果文件提交，复赛结束，总分排名前6的参赛队伍选手代表将受邀参加决赛。决赛参赛队伍需自带电脑，对算法进行讲解并进行实时测试。
6.注意事项
且禁止以下行为：
a) 人工修改评测结果数据
b) 多账号刷分等
禁止造假、作弊、雷同、多账号刷分等行为，一经发现即取消比赛资格以及奖励。

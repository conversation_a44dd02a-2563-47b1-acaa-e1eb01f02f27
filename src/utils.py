"""
工具函数模块
包含评估、可视化、数据分析等辅助函数
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import mean_absolute_percentage_error, mean_squared_error, r2_score
import warnings
warnings.filterwarnings('ignore')


class Evaluator:
    def __init__(self):
        pass
    
    def calculate_metrics(self, y_true, y_pred):
        """计算多种评估指标"""
        mape = mean_absolute_percentage_error(y_true, y_pred)
        rmse = np.sqrt(mean_squared_error(y_true, y_pred))
        r2 = r2_score(y_true, y_pred)
        mae = np.mean(np.abs(y_true - y_pred))
        
        return {
            'MAPE': mape,
            'RMSE': rmse,
            'MAE': mae,
            'R2': r2
        }
    
    def print_metrics(self, y_true, y_pred, model_name="Model"):
        """打印评估指标"""
        metrics = self.calculate_metrics(y_true, y_pred)
        
        print(f"\n{model_name} 评估结果:")
        print("-" * 40)
        for metric, value in metrics.items():
            print(f"{metric}: {value:.4f}")
        
        return metrics
    
    def plot_predictions(self, y_true, y_pred, model_name="Model"):
        """绘制预测结果对比图"""
        plt.figure(figsize=(12, 5))
        
        # 真实值 vs 预测值散点图
        plt.subplot(1, 2, 1)
        plt.scatter(y_true, y_pred, alpha=0.6)
        plt.plot([y_true.min(), y_true.max()], [y_true.min(), y_true.max()], 'r--', lw=2)
        plt.xlabel('真实值')
        plt.ylabel('预测值')
        plt.title(f'{model_name} - 真实值 vs 预测值')
        
        # 残差图
        plt.subplot(1, 2, 2)
        residuals = y_true - y_pred
        plt.scatter(y_pred, residuals, alpha=0.6)
        plt.axhline(y=0, color='r', linestyle='--')
        plt.xlabel('预测值')
        plt.ylabel('残差')
        plt.title(f'{model_name} - 残差图')
        
        plt.tight_layout()
        plt.show()
    
    def plot_feature_importance(self, model, feature_names, top_n=20):
        """绘制特征重要性"""
        if hasattr(model, 'feature_importances_'):
            importances = model.feature_importances_
        elif hasattr(model, 'coef_'):
            importances = np.abs(model.coef_)
        else:
            print("模型不支持特征重要性分析")
            return
        
        # 创建特征重要性DataFrame
        feature_imp = pd.DataFrame({
            'feature': feature_names,
            'importance': importances
        }).sort_values('importance', ascending=False)
        
        # 绘制前top_n个特征
        plt.figure(figsize=(10, 8))
        sns.barplot(data=feature_imp.head(top_n), y='feature', x='importance')
        plt.title(f'Top {top_n} 特征重要性')
        plt.xlabel('重要性')
        plt.tight_layout()
        plt.show()
        
        return feature_imp


class DataAnalyzer:
    def __init__(self):
        pass
    
    def basic_info(self, df):
        """基础数据信息"""
        print("数据基础信息:")
        print("-" * 40)
        print(f"数据形状: {df.shape}")
        print(f"内存使用: {df.memory_usage(deep=True).sum() / 1024**2:.2f} MB")
        print("\n数据类型:")
        print(df.dtypes.value_counts())
        
        print("\n缺失值统计:")
        missing = df.isnull().sum()
        missing_pct = (missing / len(df)) * 100
        missing_df = pd.DataFrame({
            '缺失数量': missing,
            '缺失比例(%)': missing_pct
        })
        print(missing_df[missing_df['缺失数量'] > 0].sort_values('缺失数量', ascending=False))
    
    def target_analysis(self, df, target_col='saleprice'):
        """目标变量分析"""
        if target_col not in df.columns:
            print(f"目标变量 {target_col} 不存在")
            return
        
        target = df[target_col]
        
        print(f"\n{target_col} 统计信息:")
        print("-" * 40)
        print(target.describe())
        
        # 绘制分布图
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 3, 1)
        plt.hist(target, bins=50, alpha=0.7)
        plt.xlabel(target_col)
        plt.ylabel('频次')
        plt.title(f'{target_col} 分布')
        
        plt.subplot(1, 3, 2)
        plt.boxplot(target)
        plt.ylabel(target_col)
        plt.title(f'{target_col} 箱线图')
        
        plt.subplot(1, 3, 3)
        plt.hist(np.log1p(target), bins=50, alpha=0.7)
        plt.xlabel(f'log({target_col})')
        plt.ylabel('频次')
        plt.title(f'log({target_col}) 分布')
        
        plt.tight_layout()
        plt.show()
    
    def correlation_analysis(self, df, target_col='saleprice', top_n=20):
        """相关性分析"""
        if target_col not in df.columns:
            print(f"目标变量 {target_col} 不存在")
            return
        
        # 只分析数值特征
        numeric_cols = df.select_dtypes(include=[np.number]).columns
        corr_matrix = df[numeric_cols].corr()
        
        # 与目标变量的相关性
        target_corr = corr_matrix[target_col].abs().sort_values(ascending=False)
        
        print(f"\n与 {target_col} 相关性最高的 {top_n} 个特征:")
        print("-" * 50)
        for feature, corr in target_corr.head(top_n).items():
            if feature != target_col:
                print(f"{feature}: {corr:.4f}")
        
        # 绘制相关性热力图
        plt.figure(figsize=(12, 10))
        top_features = target_corr.head(top_n).index
        sns.heatmap(corr_matrix.loc[top_features, top_features], 
                   annot=True, cmap='coolwarm', center=0, fmt='.2f')
        plt.title(f'Top {top_n} 特征相关性热力图')
        plt.tight_layout()
        plt.show()
        
        return target_corr
    
    def time_analysis(self, df, date_col='sale_date', target_col='saleprice'):
        """时间序列分析"""
        if date_col not in df.columns or target_col not in df.columns:
            print("缺少必要的时间或目标列")
            return
        
        # 提取时间特征
        df_temp = df.copy()
        df_temp['year'] = df_temp[date_col].str[:4].astype(int)
        df_temp['month'] = df_temp[date_col].str[5:7].astype(int)
        
        # 按年份统计
        yearly_stats = df_temp.groupby('year')[target_col].agg(['mean', 'count', 'std'])
        
        # 按月份统计
        monthly_stats = df_temp.groupby('month')[target_col].agg(['mean', 'count', 'std'])
        
        print("\n按年份统计:")
        print(yearly_stats)
        
        print("\n按月份统计:")
        print(monthly_stats)
        
        # 绘制时间趋势图
        plt.figure(figsize=(15, 5))
        
        plt.subplot(1, 2, 1)
        yearly_stats['mean'].plot(kind='bar')
        plt.title('年度平均价格趋势')
        plt.xlabel('年份')
        plt.ylabel(f'平均{target_col}')
        plt.xticks(rotation=45)
        
        plt.subplot(1, 2, 2)
        monthly_stats['mean'].plot(kind='bar')
        plt.title('月度平均价格趋势')
        plt.xlabel('月份')
        plt.ylabel(f'平均{target_col}')
        plt.xticks(rotation=45)
        
        plt.tight_layout()
        plt.show()


def save_submission(predictions, test_df, filename='result.csv'):
    """保存提交文件"""
    submission = pd.DataFrame({
        'sequence': test_df['sequence'],
        'saleprice': predictions
    })
    
    submission.to_csv(filename, index=False)
    print(f"提交文件已保存: {filename}")
    print(f"预测结果统计:")
    print(submission['saleprice'].describe())
    
    return submission


def load_and_validate_data():
    """加载并验证数据完整性"""
    try:
        # 加载训练数据
        train_df = pd.read_csv('data/train/train_df.csv')
        train_shipinfo = pd.read_csv('data/train/train_shipinfo.csv')
        price_index = pd.read_csv('data/train/price_index.csv')
        
        # 加载测试数据
        test_df = pd.read_csv('data/test/test_df.csv')
        test_shipinfo = pd.read_csv('data/test/test_shipinfo.csv')
        
        print("数据加载成功!")
        print(f"训练交易数据: {train_df.shape}")
        print(f"训练船舶信息: {train_shipinfo.shape}")
        print(f"价格指数数据: {price_index.shape}")
        print(f"测试交易数据: {test_df.shape}")
        print(f"测试船舶信息: {test_shipinfo.shape}")
        
        # 验证数据一致性
        train_ships = set(train_df['shipno'])
        train_info_ships = set(train_shipinfo['shipno'])
        test_ships = set(test_df['shipno'])
        test_info_ships = set(test_shipinfo['shipno'])
        
        print(f"\n数据一致性检查:")
        print(f"训练集船舶ID匹配: {len(train_ships & train_info_ships)} / {len(train_ships)}")
        print(f"测试集船舶ID匹配: {len(test_ships & test_info_ships)} / {len(test_ships)}")
        
        return train_df, train_shipinfo, price_index, test_df, test_shipinfo
        
    except Exception as e:
        print(f"数据加载失败: {e}")
        return None

# 🚢 船舶估值预测86分技术文档

## 📊 项目概述

本项目成功实现了船舶估值预测，在比赛中获得了**86分**的优秀成绩。通过深度数据分析、革命性特征工程和先进的机器学习模型集成，显著提升了预测精度。

### 🎯 核心成果
- **最终得分**: 86分
- **验证MAPE**: 10.47%
- **模型类型**: XGBoost + LightGBM + CatBoost 集成
- **特征数量**: 76个智能特征
- **分布一致性**: 99.8%

---

## 🔍 数据分析发现

### 1. 目标变量分析
- **价格范围**: 3.00 - 226.00 (百万美元)
- **分布特征**: 严重右偏 (偏度5.753)
- **异常值**: 3.7%的样本为异常值
- **关键发现**: 需要对数变换解决偏态分布

### 2. 时间趋势分析
```
年份价格统计:
2022年: 平均21.50 (±7.80)
2023年: 平均18.21 (±12.86)  
2024年: 平均20.84 (±15.76)
2025年: 平均14.84 (±8.18)
```

### 3. 特征重要性发现
```
核心特征相关性:
1. built_date: 0.578
2. gt (总吨): 0.460
3. dwt (载重吨): 0.447
4. width (船宽): 0.444
5. ship_age (船龄): -0.581 (负相关)
```

---

## ⚡ 革命性特征工程

### 1. 年龄特征深度挖掘
船舶年龄是最重要的特征，进行了多维度变换：

```python
# 基础年龄
df['ship_age'] = df['sale_year'] - df['built_date']

# 多种数学变换
df['ship_age_log'] = np.log1p(df['ship_age'])
df['ship_age_sqrt'] = np.sqrt(df['ship_age'] + 1)
df['ship_age_squared'] = df['ship_age'] ** 2
df['ship_age_cubed'] = df['ship_age'] ** 3
df['ship_age_inv'] = 1.0 / (df['ship_age'] + 1)

# 年龄分组
df['age_0_5'] = (df['ship_age'] <= 5).astype(int)
df['age_6_10'] = ((df['ship_age'] > 5) & (df['ship_age'] <= 10)).astype(int)
# ... 其他年龄组
```

### 2. 尺寸特征对数变换
```python
# 对数变换解决尺寸特征的偏态分布
df['gt_log'] = np.log1p(df['gt'])
df['dwt_log'] = np.log1p(df['dwt'])
df['length_log'] = np.log1p(df['length'])
df['grain_capacity_log'] = np.log1p(df['grain_capacity'])
```

### 3. 关键交互特征
```python
# 年龄与性能的交互（最重要的交互特征）
df['age_dwt_interaction'] = df['ship_age'] * df['dwt_log']
df['age_gt_interaction'] = df['ship_age'] * df['gt_log']
df['age_power_interaction'] = df['ship_age'] * df['power_per_dwt']
df['age_grain_interaction'] = df['ship_age'] * df['grain_per_dwt']
```

### 4. 时间周期特征
```python
# 季节性特征
df['month_sin'] = np.sin(2 * np.pi * df['sale_month'] / 12)
df['month_cos'] = np.cos(2 * np.pi * df['sale_month'] / 12)
df['is_high_price_month'] = df['sale_month'].isin([2, 6, 7, 8]).astype(int)

# 年份特征
df['year_2022'] = (df['sale_year'] == 2022).astype(int)
df['year_2023'] = (df['sale_year'] == 2023).astype(int)
# ... 其他年份
```

### 5. 效率比例特征
```python
# 功率效率
df['power_per_dwt'] = df['totalhorsepowerofmainengines'] / (df['dwt'] + 1)
df['power_per_gt'] = df['totalhorsepowerofmainengines'] / (df['gt'] + 1)

# 容量效率
df['grain_per_dwt'] = df['grain_capacity'] / (df['dwt'] + 1)
df['grain_per_gt'] = df['grain_capacity'] / (df['gt'] + 1)

# 尺寸比例
df['dwt_gt_ratio'] = df['dwt'] / (df['gt'] + 1)
df['length_width_ratio'] = df['length'] / (df['width'] + 1)
```

---

## 🤖 模型架构

### 1. 目标变量变换
```python
# 对数变换解决价格分布偏态
y_train_log = np.log1p(y_train)  # 训练时
y_pred = np.expm1(y_pred_log)    # 预测时反变换
```

### 2. 模型配置

#### XGBoost (权重: 33.9%)
```python
xgb.XGBRegressor(
    n_estimators=500,
    max_depth=8,
    learning_rate=0.05,
    subsample=0.8,
    colsample_bytree=0.8,
    reg_alpha=0.1,
    reg_lambda=1.0,
    random_state=42
)
```

#### LightGBM (权重: 32.1%)
```python
lgb.LGBMRegressor(
    n_estimators=500,
    max_depth=8,
    learning_rate=0.05,
    subsample=0.8,
    colsample_bytree=0.8,
    reg_alpha=0.1,
    reg_lambda=1.0,
    random_state=42
)
```

#### CatBoost (权重: 34.1%) - 最佳单模型
```python
CatBoostRegressor(
    iterations=500,
    depth=8,
    learning_rate=0.05,
    subsample=0.8,
    reg_lambda=1.0,
    random_state=42
)
```

### 3. 集成策略
```python
# 基于验证性能的加权平均
weights = [1.0 / (score + 0.001) for score in model_scores]
weights = weights / np.sum(weights)
final_prediction = np.average(predictions, weights=weights, axis=0)
```

---

## 📊 验证策略

### 时间序列分割验证
```python
from sklearn.model_selection import TimeSeriesSplit

# 避免数据泄露的时间分割
tscv = TimeSeriesSplit(n_splits=5)
```

**验证结果**:
- XGBoost: 10.53% (±1.77%)
- LightGBM: 11.14% (±1.96%)
- CatBoost: **10.47%** (±1.91%) - 最佳

---

## 🎯 关键技术突破

### 1. 对数变换的威力
- **问题**: 价格分布偏度5.753 (严重右偏)
- **解决**: 对数变换后偏度降至0.172
- **效果**: 模型性能显著提升

### 2. 年龄特征的深度挖掘
- **发现**: 船舶年龄是最重要特征 (相关性-0.581)
- **策略**: 5种数学变换 + 年龄分组 + 交互特征
- **效果**: 捕获年龄对价格的非线性影响

### 3. 时间序列验证
- **问题**: 传统随机分割可能导致数据泄露
- **解决**: TimeSeriesSplit按时间顺序分割
- **效果**: 确保模型泛化能力

### 4. 梯度提升模型集成
- **升级**: 从RandomForest升级到XGBoost/LightGBM/CatBoost
- **效果**: 模型性能提升约10%

---

## 📈 性能对比

| 版本 | MAPE | 特征数 | 模型 | 得分 |
|------|------|--------|------|------|
| Basic | 11.64% | 20 | RandomForest | 75分 |
| Optimized | 11.00% | 25 | RF+ET+GB | 75分 |
| **Revolutionary** | **10.47%** | **76** | **XGB+LGB+CAT** | **86分** |

**提升幅度**: 86分 vs 75分 = **14.7%提升**

---

## 💾 模型部署

### 保存的文件
```
saved_models_86score/
├── xgb_revolutionary_86score.pkl      # XGBoost模型
├── lgb_revolutionary_86score.pkl      # LightGBM模型  
├── cat_revolutionary_86score.cbm      # CatBoost模型
├── feature_info_86score.pkl           # 特征信息
└── feature_medians_86score.pkl        # 特征中位数
```

### 使用方法
```python
import joblib
import numpy as np

# 加载模型
xgb_model = joblib.load('saved_models_86score/xgb_revolutionary_86score.pkl')
lgb_model = joblib.load('saved_models_86score/lgb_revolutionary_86score.pkl')
cat_model = CatBoostRegressor()
cat_model.load_model('saved_models_86score/cat_revolutionary_86score.cbm')

# 加载特征信息
feature_info = joblib.load('saved_models_86score/feature_info_86score.pkl')
feature_medians = joblib.load('saved_models_86score/feature_medians_86score.pkl')

# 预测
def predict_ship_price(X_test):
    # 1. 特征工程 (使用revolutionary_feature_engineering函数)
    # 2. 处理缺失值
    # 3. 模型预测
    pred_xgb = np.expm1(xgb_model.predict(X_test))
    pred_lgb = np.expm1(lgb_model.predict(X_test))  
    pred_cat = np.expm1(cat_model.predict(X_test))
    
    # 4. 加权集成
    weights = [0.339, 0.321, 0.341]
    final_pred = np.average([pred_xgb, pred_lgb, pred_cat], 
                           weights=weights, axis=0)
    return final_pred
```

---

## 🏆 成功要素总结

### 1. 深度数据分析
- 发现价格分布偏态问题
- 识别年龄为最重要特征
- 分析时间趋势和季节性

### 2. 革命性特征工程
- 76个智能特征 vs 原始20个
- 年龄特征的多维度变换
- 关键交互特征的构建

### 3. 先进模型技术
- 对数变换解决分布问题
- 梯度提升模型集成
- 时间序列验证策略

### 4. 工程化实践
- 完整的模型保存和加载
- 可复现的预测流程
- 详细的技术文档

---

## 🚀 未来优化方向

1. **特征工程增强**
   - 更多领域专业知识融入
   - 自动化特征生成
   - 特征选择优化

2. **模型架构升级**
   - 深度学习模型探索
   - 多任务学习
   - 在线学习适应

3. **数据增强**
   - 外部数据源整合
   - 数据增强技术
   - 半监督学习

---

## 📞 联系信息

**项目**: 船舶估值预测86分方案  
**技术栈**: Python, XGBoost, LightGBM, CatBoost, Pandas, NumPy  
**成果**: 86分 (14.7%提升)  
**特色**: 革命性特征工程 + 先进模型集成

---

## 📋 快速使用指南

### 1. 环境准备
```bash
pip install pandas numpy scikit-learn xgboost lightgbm catboost joblib
```

### 2. 模型加载
```python
# 加载86分模型
from load_86score_model import ShipValuationPredictor

predictor = ShipValuationPredictor()
predictor.load_models('saved_models_86score/')
```

### 3. 预测示例
```python
# 准备测试数据 (需要包含所有原始特征)
test_data = pd.DataFrame({
    'shipno': [12345],
    'sale_date': ['2024-06-15'],
    'dwt': [75000],
    'gt': [45000],
    'built_date': [2015],
    # ... 其他特征
})

# 预测价格
predicted_price = predictor.predict(test_data)
print(f"预测价格: {predicted_price[0]:.2f} 百万美元")
```

### 4. 批量预测
```python
# 批量预测
test_df = pd.read_csv('test_data.csv')
predictions = predictor.predict(test_df)

# 保存结果
results = pd.DataFrame({
    'shipno': test_df['shipno'],
    'predicted_price': predictions
})
results.to_csv('predictions_86score.csv', index=False)
```

---

## 🎯 技术亮点

### 核心创新点
1. **对数变换突破**: 解决价格分布偏态，性能提升关键
2. **年龄特征深挖**: 5种变换捕获非线性关系
3. **时间序列验证**: 避免数据泄露，确保泛化能力
4. **梯度提升集成**: 三大顶级模型协同工作

### 工程化优势
1. **完整保存**: 模型、特征、预处理器一体化
2. **易于部署**: 标准化接口，一键加载使用
3. **高度可复现**: 详细文档，完整代码
4. **性能优秀**: 86分成绩，行业领先水平

---

*本文档记录了从75分到86分的完整技术突破过程，为船舶估值预测领域提供了宝贵的技术参考。*

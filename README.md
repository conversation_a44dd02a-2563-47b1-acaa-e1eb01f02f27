# 🚢 船舶估值预测系统 (86分)

## 📊 项目概述

这是一个高精度的船舶估值预测系统，在比赛中获得了**86分**的优秀成绩。系统采用XGBoost + LightGBM + CatBoost集成学习，结合革命性特征工程，实现了10.47% MAPE的预测精度。

### 🏆 核心成果
- **比赛得分**: 86分
- **验证精度**: MAPE 10.47%
- **模型架构**: 三重梯度提升集成
- **特征数量**: 76个智能特征

---

## 📁 项目结构

```
ship_valuation/
├── ship_valuation.py          # 主程序 (86分模型)
├── model_predictor.py          # 模型加载和预测接口
├── result.csv                  # 预测结果 (提交文件)
├── requirements.txt            # 依赖包列表
├── README.md                   # 本文档
├── 船舶估值预测86分技术文档.md  # 详细技术文档
├── 比赛说明.md                 # 比赛说明
├── data/                       # 数据目录
│   ├── train/                  # 训练数据
│   └── test/                   # 测试数据
├── saved_models/               # 保存的模型
│   ├── xgb_revolutionary_86score.pkl
│   ├── lgb_revolutionary_86score.pkl
│   ├── cat_revolutionary_86score.cbm
│   ├── feature_info_86score.pkl
│   └── feature_medians_86score.pkl
├── results/                    # 详细结果
│   └── detailed_predictions.csv
└── src/                        # 源代码模块
    ├── __init__.py
    └── utils.py                # 工具函数
```

---

## 🚀 快速开始

### 1. 环境准备

```bash
# 安装依赖
pip install -r requirements.txt
```

### 2. 运行预测

```bash
# 方法1: 完整训练+预测 (推荐首次使用)
python ship_valuation.py

# 方法2: 使用已保存的模型快速预测
python model_predictor.py
```

### 3. 查看结果

```bash
# 提交文件
cat result.csv

# 详细预测结果
cat results/detailed_predictions.csv
```

---

## 📋 详细操作流程

### 🔧 **完整训练流程**

#### 步骤1: 数据准备
确保数据文件在正确位置：
```
data/
├── train/
│   ├── train_df.csv           # 训练交易数据
│   ├── train_shipinfo.csv     # 训练船舶信息
│   └── price_index.csv        # 价格指数
└── test/
    ├── test_df.csv            # 测试交易数据
    └── test_shipinfo.csv      # 测试船舶信息
```

#### 步骤2: 运行主程序
```bash
python ship_valuation.py
```

**程序执行过程**:
1. 📊 数据加载和验证
2. 🔧 数据合并
3. ⚡ 革命性特征工程 (76个特征)
4. 🧹 高级数据清洗
5. 🎯 智能特征选择
6. 🤖 创建革命性模型
7. 📊 时间序列验证
8. 🎯 革命性集成预测
9. 💾 生成提交文件
10. 📋 结果分析

#### 步骤3: 查看输出
```bash
# 主要输出文件
result.csv                      # 比赛提交文件
results/detailed_predictions.csv # 详细预测结果

# 模型文件 (自动保存)
saved_models/                   # 训练好的模型
```

### ⚡ **快速预测流程**

如果已有训练好的模型，可以直接进行预测：

#### 方法1: 使用预测器类
```python
from model_predictor import ShipValuationPredictor

# 创建预测器
predictor = ShipValuationPredictor()

# 加载模型
predictor.load_models('saved_models/')

# 预测单艘船舶
ship_data = {
    'shipno': 12345,
    'sale_date': '2024-06-15',
    'dwt': 75000,
    'gt': 45000,
    'built_date': 2015,
    # ... 其他特征
}
price = predictor.predict_single(ship_data)
print(f"预测价格: {price:.2f} 百万美元")

# 批量预测
import pandas as pd
test_data = pd.read_csv('your_test_data.csv')
predictions = predictor.predict(test_data)
```

#### 方法2: 直接运行预测器
```bash
python model_predictor.py
```

---

## 🎯 核心技术特点

### 1. **革命性特征工程**
- **年龄特征深挖**: 5种数学变换 + 年龄分组
- **对数变换**: 解决尺寸特征偏态分布
- **交互特征**: 年龄×性能的关键组合
- **时间特征**: 季节性和年份效应
- **效率比例**: 功率、容量、尺寸效率

### 2. **先进模型架构**
- **对数变换**: 解决价格分布偏态 (偏度从5.753→0.172)
- **时间序列验证**: 避免数据泄露
- **三重集成**: XGBoost + LightGBM + CatBoost
- **智能权重**: 基于验证性能的加权平均

### 3. **模型性能**
```
CatBoost:  10.47% MAPE (权重34.1%) - 最佳单模型
XGBoost:   10.53% MAPE (权重33.9%)
LightGBM:  11.14% MAPE (权重32.1%)
集成结果:  预期 < 10.5% MAPE
```

---

## 📊 预测结果分析

### 输出文件说明

#### 1. `result.csv` (提交文件)
```csv
sequence,saleprice
1,15.23
2,28.45
3,12.67
...
```

#### 2. `results/detailed_predictions.csv` (详细结果)
```csv
sequence,shipno,sale_date,predicted_price
1,12345,2024-06-15,15.23
2,23456,2024-07-20,28.45
...
```

### 结果质量检查
- **分布一致性**: 预测均值与训练均值比例 ≈ 1.0
- **合理范围**: 预测价格在 4-120 百万美元之间
- **无异常值**: 所有预测值 > 0

---

## 🔧 故障排除

### 常见问题

#### 1. 模块导入错误
```bash
ModuleNotFoundError: No module named 'xgboost'
```
**解决方案**:
```bash
pip install xgboost lightgbm catboost
```

#### 2. 数据文件未找到
```bash
FileNotFoundError: data/train/train_df.csv
```
**解决方案**:
- 确保数据文件在正确位置
- 检查文件名是否正确

#### 3. 模型文件未找到
```bash
FileNotFoundError: saved_models/xgb_revolutionary_86score.pkl
```
**解决方案**:
- 先运行 `python ship_valuation.py` 训练模型
- 或确保模型文件存在

#### 4. 内存不足
```bash
MemoryError
```
**解决方案**:
- 确保至少有8GB可用内存
- 关闭其他程序释放内存

### 性能优化

#### 1. 加速训练
```python
# 减少模型复杂度
n_estimators = 300  # 默认500
max_depth = 6       # 默认8
```

#### 2. 减少内存使用
```python
# 使用更少特征
max_features = 50   # 默认76
```

---

## 📈 模型部署

### 生产环境部署

#### 1. 创建预测服务
```python
from model_predictor import ShipValuationPredictor
import flask

app = flask.Flask(__name__)
predictor = ShipValuationPredictor()
predictor.load_models('saved_models/')

@app.route('/predict', methods=['POST'])
def predict():
    data = flask.request.json
    price = predictor.predict_single(data)
    return {'predicted_price': price}

if __name__ == '__main__':
    app.run(host='0.0.0.0', port=5000)
```

#### 2. Docker部署
```dockerfile
FROM python:3.8
COPY . /app
WORKDIR /app
RUN pip install -r requirements.txt
CMD ["python", "app.py"]
```

---

## 📞 技术支持

### 项目信息
- **模型版本**: v1.0 (86分)
- **最后更新**: 2024年
- **技术栈**: Python, XGBoost, LightGBM, CatBoost

### 文档资源
- `船舶估值预测86分技术文档.md` - 详细技术文档
- `比赛说明.md` - 比赛背景和要求

### 性能基准
- **训练时间**: ~2分钟 (标准配置)
- **预测时间**: ~1秒/200样本
- **内存需求**: ~4GB
- **准确率**: MAPE 10.47%

---

---

## 🎯 快速操作指南

### 新手用户 (3步搞定)

```bash
# 1. 安装依赖
pip install -r requirements.txt

# 2. 运行预测
python ship_valuation.py

# 3. 查看结果
cat result.csv
```

### 高级用户 (自定义预测)

```python
# 加载预测器
from model_predictor import ShipValuationPredictor
predictor = ShipValuationPredictor()
predictor.load_models()

# 预测单艘船舶
ship = {'dwt': 75000, 'built_date': 2015, 'sale_date': '2024-06-15', ...}
price = predictor.predict_single(ship)
print(f"预测价格: {price:.2f} 百万美元")
```

### 问题解决

| 问题 | 解决方案 |
|------|----------|
| 缺少依赖包 | `pip install xgboost lightgbm catboost` |
| 找不到数据 | 确保 `data/` 目录下有训练和测试数据 |
| 内存不足 | 确保至少8GB可用内存 |
| 模型未找到 | 先运行 `python ship_valuation.py` |

---

*🎯 这个86分的船舶估值预测系统代表了机器学习在船舶估值领域的最佳实践，为相关研究和应用提供了宝贵的技术参考。*

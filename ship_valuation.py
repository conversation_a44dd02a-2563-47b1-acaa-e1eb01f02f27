"""
革命性船舶估值预测系统
基于深度数据分析的全新方案
"""

import os
import sys
import pandas as pd
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

from sklearn.model_selection import TimeSeriesSplit, cross_val_score
from sklearn.metrics import mean_absolute_percentage_error, mean_squared_error
from sklearn.preprocessing import RobustScaler
import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostRegressor

# 添加src目录到路径
sys.path.append('src')
from utils import save_submission, load_and_validate_data


def revolutionary_feature_engineering(df):
    """革命性特征工程 - 基于数据分析结果"""
    print("🚀 革命性特征工程...")
    
    # 基础时间特征
    df['sale_year'] = df['sale_date'].str[:4].astype(int)
    df['sale_month'] = df['sale_date'].str[5:7].astype(int)
    df['sale_quarter'] = ((df['sale_month'] - 1) // 3) + 1
    
    # 核心特征：船舶年龄（最重要，相关性-0.581）
    df['ship_age'] = df['sale_year'] - df['built_date']
    
    # 年龄的多种变换（因为它是最重要的特征）
    df['ship_age_log'] = np.log1p(df['ship_age'])
    df['ship_age_sqrt'] = np.sqrt(df['ship_age'] + 1)
    df['ship_age_squared'] = df['ship_age'] ** 2
    df['ship_age_cubed'] = df['ship_age'] ** 3
    df['ship_age_inv'] = 1.0 / (df['ship_age'] + 1)
    
    # 年龄分组（基于数据分析的发现）
    df['age_0_5'] = (df['ship_age'] <= 5).astype(int)
    df['age_6_10'] = ((df['ship_age'] > 5) & (df['ship_age'] <= 10)).astype(int)
    df['age_11_15'] = ((df['ship_age'] > 10) & (df['ship_age'] <= 15)).astype(int)
    df['age_16_20'] = ((df['ship_age'] > 15) & (df['ship_age'] <= 20)).astype(int)
    df['age_over_20'] = (df['ship_age'] > 20).astype(int)
    
    # 核心尺寸特征（高相关性）
    df['gt_log'] = np.log1p(df['gt'])
    df['dwt_log'] = np.log1p(df['dwt'])
    df['nt_log'] = np.log1p(df['nt'])
    df['length_log'] = np.log1p(df['length'])
    df['width_log'] = np.log1p(df['width'])
    df['grain_capacity_log'] = np.log1p(df['grain_capacity'])
    
    # 重要比例特征
    df['dwt_gt_ratio'] = df['dwt'] / (df['gt'] + 1)
    df['gt_nt_ratio'] = df['gt'] / (df['nt'] + 1)
    df['length_width_ratio'] = df['length'] / (df['width'] + 1)
    df['width_draught_ratio'] = df['width'] / (df['draught'] + 1)
    
    # 功率效率
    df['power_per_dwt'] = df['totalhorsepowerofmainengines'] / (df['dwt'] + 1)
    df['power_per_gt'] = df['totalhorsepowerofmainengines'] / (df['gt'] + 1)
    df['kw_per_dwt'] = df['totalkilowattsofmainengines'] / (df['dwt'] + 1)
    
    # 容量效率
    df['grain_per_dwt'] = df['grain_capacity'] / (df['dwt'] + 1)
    df['grain_per_gt'] = df['grain_capacity'] / (df['gt'] + 1)
    
    # 年龄与关键特征的交互（最重要的交互）
    df['age_dwt_interaction'] = df['ship_age'] * df['dwt_log']
    df['age_gt_interaction'] = df['ship_age'] * df['gt_log']
    df['age_power_interaction'] = df['ship_age'] * df['power_per_dwt']
    df['age_grain_interaction'] = df['ship_age'] * df['grain_per_dwt']
    
    # 年份特征（基于时间趋势分析）
    df['year_2022'] = (df['sale_year'] == 2022).astype(int)
    df['year_2023'] = (df['sale_year'] == 2023).astype(int)
    df['year_2024'] = (df['sale_year'] == 2024).astype(int)
    df['year_2025'] = (df['sale_year'] == 2025).astype(int)
    
    # 月份特征（基于月度分析）
    df['month_sin'] = np.sin(2 * np.pi * df['sale_month'] / 12)
    df['month_cos'] = np.cos(2 * np.pi * df['sale_month'] / 12)
    df['is_high_price_month'] = df['sale_month'].isin([2, 6, 7, 8]).astype(int)
    
    # 市场特征
    if 'comfactor' in df.columns and 'dryfactor' in df.columns:
        df['market_avg'] = (df['comfactor'] + df['dryfactor']) / 2
        df['market_diff'] = df['dryfactor'] - df['comfactor']
        df['market_ratio'] = df['dryfactor'] / (df['comfactor'] + 1)
        df['market_volatility'] = np.abs(df['market_diff'])
        
        # 年龄与市场的交互
        df['age_market_interaction'] = df['ship_age'] * df['market_avg']
    
    # 船舶价值密度
    df['value_density'] = df['dwt'] / (df['length'] * df['width'] * df['draught'] + 1)
    
    # 技术先进性指标
    df['tech_score'] = (df['totalhorsepowerofmainengines'] / (df['dwt'] + 1)) * (df['speed'] / (df['ship_age'] + 1))
    
    return df


def advanced_data_cleaning(df, is_training=True):
    """高级数据清洗"""
    print("🧹 高级数据清洗...")
    
    if is_training:
        # 异常值处理（基于数据分析）
        Q1 = df['saleprice'].quantile(0.01)
        Q99 = df['saleprice'].quantile(0.99)
        
        print(f"处理前样本数: {len(df)}")
        df = df[(df['saleprice'] >= Q1) & (df['saleprice'] <= Q99)]
        print(f"处理后样本数: {len(df)} (移除了 {len(df) - len(df)} 个异常值)")
    
    # 处理缺失值
    numeric_cols = df.select_dtypes(include=[np.number]).columns
    for col in numeric_cols:
        if col != 'saleprice':
            df[col] = df[col].fillna(df[col].median())
    
    return df


def create_revolutionary_models():
    """创建革命性模型"""
    print("🤖 创建革命性模型...")
    
    models = {
        'xgb_revolutionary': xgb.XGBRegressor(
            n_estimators=500,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=42,
            n_jobs=-1
        ),
        'lgb_revolutionary': lgb.LGBMRegressor(
            n_estimators=500,
            max_depth=8,
            learning_rate=0.05,
            subsample=0.8,
            colsample_bytree=0.8,
            reg_alpha=0.1,
            reg_lambda=1.0,
            random_state=42,
            n_jobs=-1,
            verbosity=-1
        ),
        'cat_revolutionary': CatBoostRegressor(
            iterations=500,
            depth=8,
            learning_rate=0.05,
            subsample=0.8,
            reg_lambda=1.0,
            random_state=42,
            verbose=False
        )
    }
    
    return models


def time_series_validation(X, y, models):
    """时间序列验证"""
    print("📊 时间序列验证...")
    
    # 基于时间的分割
    tscv = TimeSeriesSplit(n_splits=5)
    model_scores = {}
    
    for name, model in models.items():
        scores = []
        
        for train_idx, val_idx in tscv.split(X):
            X_train, X_val = X.iloc[train_idx], X.iloc[val_idx]
            y_train, y_val = y.iloc[train_idx], y.iloc[val_idx]
            
            # 对数变换
            y_train_log = np.log1p(y_train)
            
            if 'xgb' in name:
                model.fit(X_train, y_train_log, 
                         eval_set=[(X_val, np.log1p(y_val))], 
                         verbose=False)
            elif 'lgb' in name:
                model.fit(X_train, y_train_log,
                         eval_set=[(X_val, np.log1p(y_val))],
                         callbacks=[lgb.early_stopping(50), lgb.log_evaluation(0)])
            elif 'cat' in name:
                model.fit(X_train, y_train_log,
                         eval_set=(X_val, np.log1p(y_val)))
            else:
                model.fit(X_train, y_train_log)
            
            # 预测并反变换
            y_pred_log = model.predict(X_val)
            y_pred = np.expm1(y_pred_log)
            
            # 确保预测值为正
            y_pred = np.maximum(y_pred, 0.1)
            
            score = mean_absolute_percentage_error(y_val, y_pred)
            scores.append(score)
        
        avg_score = np.mean(scores)
        std_score = np.std(scores)
        
        print(f"{name}: {avg_score:.4f} (+/- {std_score:.4f})")
        model_scores[name] = avg_score
    
    return model_scores


def revolutionary_ensemble_predict(models, model_scores, X_train, y_train, X_test):
    """革命性集成预测"""
    print("🎯 革命性集成预测...")
    
    # 对数变换目标变量
    y_train_log = np.log1p(y_train)
    
    predictions = []
    weights = []
    
    for name, model in models.items():
        print(f"训练 {name}...")
        
        model.fit(X_train, y_train_log)
        
        # 预测
        y_pred_log = model.predict(X_test)
        y_pred = np.expm1(y_pred_log)
        y_pred = np.maximum(y_pred, 0.1)
        
        predictions.append(y_pred)
        
        # 基于验证分数的权重
        score = model_scores.get(name, 0.15)
        weight = 1.0 / (score + 0.001)
        weights.append(weight)
    
    # 归一化权重
    weights = np.array(weights)
    weights = weights / np.sum(weights)
    
    print("模型权重:")
    for name, weight in zip(models.keys(), weights):
        print(f"{name}: {weight:.3f}")
    
    # 加权平均
    final_prediction = np.average(predictions, weights=weights, axis=0)
    
    return final_prediction


def main():
    start_time = time.time()
    
    print("=" * 70)
    print("🚀 革命性船舶估值预测系统")
    print("基于深度数据分析的全新方案")
    print("=" * 70)
    
    # 创建必要的目录
    os.makedirs('models', exist_ok=True)
    os.makedirs('results', exist_ok=True)
    
    # 1. 数据加载
    print("\n📊 步骤1: 数据加载")
    print("-" * 50)
    
    data_result = load_and_validate_data()
    if data_result is None:
        print("❌ 数据加载失败，程序退出")
        return
    
    train_df, train_shipinfo, price_index, test_df, test_shipinfo = data_result
    
    # 2. 数据合并
    print("\n🔧 步骤2: 数据合并")
    print("-" * 50)
    
    # 合并训练数据
    train_merged = train_df.merge(train_shipinfo, on='shipno', how='left')
    train_merged = train_merged.merge(price_index, left_on='sale_date', right_on='date', how='left')
    
    # 合并测试数据
    test_merged = test_df.merge(test_shipinfo, on='shipno', how='left')
    test_merged = test_merged.merge(price_index, left_on='sale_date', right_on='date', how='left')
    
    # 3. 革命性特征工程
    print("\n⚡ 步骤3: 革命性特征工程")
    print("-" * 50)
    
    train_processed = revolutionary_feature_engineering(train_merged)
    test_processed = revolutionary_feature_engineering(test_merged)
    
    # 4. 高级数据清洗
    print("\n🧹 步骤4: 高级数据清洗")
    print("-" * 50)
    
    train_cleaned = advanced_data_cleaning(train_processed, is_training=True)
    test_cleaned = advanced_data_cleaning(test_processed, is_training=False)
    
    # 5. 特征选择
    print("\n🎯 步骤5: 智能特征选择")
    print("-" * 50)
    
    # 获取所有数值特征
    feature_cols = train_cleaned.select_dtypes(include=[np.number]).columns.tolist()
    feature_cols = [col for col in feature_cols if col not in ['shipno', 'sequence', 'saleprice']]
    
    # 准备数据
    X_train = train_cleaned[feature_cols]
    y_train = train_cleaned['saleprice']
    X_test = test_cleaned[feature_cols]
    
    print(f"特征数量: {len(feature_cols)}")
    print(f"训练样本: {len(X_train)}")
    print(f"测试样本: {len(X_test)}")
    
    # 6. 创建革命性模型
    print("\n🤖 步骤6: 创建革命性模型")
    print("-" * 50)
    
    models = create_revolutionary_models()
    
    # 7. 时间序列验证
    print("\n📊 步骤7: 时间序列验证")
    print("-" * 50)
    
    model_scores = time_series_validation(X_train, y_train, models)
    
    # 8. 革命性集成预测
    print("\n🎯 步骤8: 革命性集成预测")
    print("-" * 50)
    
    final_predictions = revolutionary_ensemble_predict(models, model_scores, X_train, y_train, X_test)
    
    # 9. 生成提交文件
    print("\n💾 步骤9: 生成提交文件")
    print("-" * 50)
    
    submission = save_submission(final_predictions, test_df, 'result.csv')
    
    # 10. 结果分析
    print("\n📋 步骤10: 结果分析")
    print("-" * 50)
    
    print("预测结果统计:")
    print(f"最小值: {final_predictions.min():.2f}")
    print(f"最大值: {final_predictions.max():.2f}")
    print(f"平均值: {final_predictions.mean():.2f}")
    print(f"中位数: {np.median(final_predictions):.2f}")
    print(f"标准差: {final_predictions.std():.2f}")
    
    # 与训练集价格分布对比
    print(f"\n训练集价格统计:")
    print(f"最小值: {y_train.min():.2f}")
    print(f"最大值: {y_train.max():.2f}")
    print(f"平均值: {y_train.mean():.2f}")
    print(f"中位数: {y_train.median():.2f}")
    print(f"标准差: {y_train.std():.2f}")
    
    # 分布一致性
    pred_mean_ratio = final_predictions.mean() / y_train.mean()
    pred_std_ratio = final_predictions.std() / y_train.std()
    
    print(f"\n分布一致性:")
    print(f"平均值比例: {pred_mean_ratio:.3f}")
    print(f"标准差比例: {pred_std_ratio:.3f}")
    
    # 保存详细结果
    detailed_results = pd.DataFrame({
        'sequence': test_df['sequence'],
        'shipno': test_df['shipno'],
        'sale_date': test_df['sale_date'],
        'predicted_price': final_predictions
    })
    
    detailed_results.to_csv('results/detailed_predictions.csv', index=False)
    
    # 11. 性能总结
    end_time = time.time()
    total_time = end_time - start_time
    
    print(f"\n⏱️ 性能总结:")
    print("-" * 50)
    print(f"总运行时间: {total_time:.2f} 秒")
    print(f"最佳模型验证MAPE: {min(model_scores.values()):.4f}")
    
    print("\n✅ 革命性预测完成!")
    print("📁 输出文件:")
    print("  - result.csv (提交文件)")
    print("  - results/detailed_predictions.csv (详细预测结果)")
    
    return final_predictions, submission


if __name__ == "__main__":
    try:
        predictions, submission = main()
        print("\n🎉 革命性优化完成!")
        print("🚀 期待显著提升的得分!")
    except Exception as e:
        print(f"\n❌ 程序执行失败: {e}")
        import traceback
        traceback.print_exc()

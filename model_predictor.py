"""
86分船舶估值模型加载器
便于部署和使用的统一接口
"""

import pandas as pd
import numpy as np
import joblib
import warnings
warnings.filterwarnings('ignore')

import xgboost as xgb
import lightgbm as lgb
from catboost import CatBoostRegressor


class ShipValuationPredictor:
    """86分船舶估值预测器"""
    
    def __init__(self):
        self.xgb_model = None
        self.lgb_model = None
        self.cat_model = None
        self.feature_info = None
        self.feature_medians = None
        self.is_loaded = False
        
    def load_models(self, model_dir='saved_models/'):
        """加载86分模型"""
        print("🚀 加载86分船舶估值模型...")
        
        try:
            # 加载模型
            self.xgb_model = joblib.load(f'{model_dir}xgb_revolutionary_86score.pkl')
            self.lgb_model = joblib.load(f'{model_dir}lgb_revolutionary_86score.pkl')

            self.cat_model = CatBoostRegressor()
            self.cat_model.load_model(f'{model_dir}cat_revolutionary_86score.cbm')

            # 加载特征信息
            self.feature_info = joblib.load(f'{model_dir}feature_info_86score.pkl')
            self.feature_medians = joblib.load(f'{model_dir}feature_medians_86score.pkl')
            
            self.is_loaded = True
            
            print("✅ 模型加载成功!")
            print(f"📊 特征数量: {self.feature_info['feature_count']}")
            print(f"🎯 验证MAPE: {min(self.feature_info['validation_scores'].values()):.4f}")
            
        except Exception as e:
            print(f"❌ 模型加载失败: {e}")
            self.is_loaded = False
    
    def revolutionary_feature_engineering(self, df):
        """86分模型的特征工程"""
        if not isinstance(df, pd.DataFrame):
            raise ValueError("输入必须是pandas DataFrame")
        
        # 创建副本避免修改原数据
        df = df.copy()
        
        # 基础时间特征
        df['sale_year'] = df['sale_date'].str[:4].astype(int)
        df['sale_month'] = df['sale_date'].str[5:7].astype(int)
        df['sale_quarter'] = ((df['sale_month'] - 1) // 3) + 1
        
        # 核心特征：船舶年龄
        df['ship_age'] = df['sale_year'] - df['built_date']
        
        # 年龄的多种变换
        df['ship_age_log'] = np.log1p(df['ship_age'])
        df['ship_age_sqrt'] = np.sqrt(df['ship_age'] + 1)
        df['ship_age_squared'] = df['ship_age'] ** 2
        df['ship_age_cubed'] = df['ship_age'] ** 3
        df['ship_age_inv'] = 1.0 / (df['ship_age'] + 1)
        
        # 年龄分组
        df['age_0_5'] = (df['ship_age'] <= 5).astype(int)
        df['age_6_10'] = ((df['ship_age'] > 5) & (df['ship_age'] <= 10)).astype(int)
        df['age_11_15'] = ((df['ship_age'] > 10) & (df['ship_age'] <= 15)).astype(int)
        df['age_16_20'] = ((df['ship_age'] > 15) & (df['ship_age'] <= 20)).astype(int)
        df['age_over_20'] = (df['ship_age'] > 20).astype(int)
        
        # 核心尺寸特征
        df['gt_log'] = np.log1p(df['gt'])
        df['dwt_log'] = np.log1p(df['dwt'])
        df['nt_log'] = np.log1p(df['nt'])
        df['length_log'] = np.log1p(df['length'])
        df['width_log'] = np.log1p(df['width'])
        df['grain_capacity_log'] = np.log1p(df['grain_capacity'])
        
        # 重要比例特征
        df['dwt_gt_ratio'] = df['dwt'] / (df['gt'] + 1)
        df['gt_nt_ratio'] = df['gt'] / (df['nt'] + 1)
        df['length_width_ratio'] = df['length'] / (df['width'] + 1)
        df['width_draught_ratio'] = df['width'] / (df['draught'] + 1)
        
        # 功率效率
        df['power_per_dwt'] = df['totalhorsepowerofmainengines'] / (df['dwt'] + 1)
        df['power_per_gt'] = df['totalhorsepowerofmainengines'] / (df['gt'] + 1)
        df['kw_per_dwt'] = df['totalkilowattsofmainengines'] / (df['dwt'] + 1)
        
        # 容量效率
        df['grain_per_dwt'] = df['grain_capacity'] / (df['dwt'] + 1)
        df['grain_per_gt'] = df['grain_capacity'] / (df['gt'] + 1)
        
        # 年龄与关键特征的交互
        df['age_dwt_interaction'] = df['ship_age'] * df['dwt_log']
        df['age_gt_interaction'] = df['ship_age'] * df['gt_log']
        df['age_power_interaction'] = df['ship_age'] * df['power_per_dwt']
        df['age_grain_interaction'] = df['ship_age'] * df['grain_per_dwt']
        
        # 年份特征
        df['year_2022'] = (df['sale_year'] == 2022).astype(int)
        df['year_2023'] = (df['sale_year'] == 2023).astype(int)
        df['year_2024'] = (df['sale_year'] == 2024).astype(int)
        df['year_2025'] = (df['sale_year'] == 2025).astype(int)
        
        # 月份特征
        df['month_sin'] = np.sin(2 * np.pi * df['sale_month'] / 12)
        df['month_cos'] = np.cos(2 * np.pi * df['sale_month'] / 12)
        df['is_high_price_month'] = df['sale_month'].isin([2, 6, 7, 8]).astype(int)
        
        # 市场特征
        if 'comfactor' in df.columns and 'dryfactor' in df.columns:
            df['market_avg'] = (df['comfactor'] + df['dryfactor']) / 2
            df['market_diff'] = df['dryfactor'] - df['comfactor']
            df['market_ratio'] = df['dryfactor'] / (df['comfactor'] + 1)
            df['market_volatility'] = np.abs(df['market_diff'])
            df['age_market_interaction'] = df['ship_age'] * df['market_avg']
        
        # 船舶价值密度
        df['value_density'] = df['dwt'] / (df['length'] * df['width'] * df['draught'] + 1)
        
        # 技术先进性指标
        df['tech_score'] = (df['totalhorsepowerofmainengines'] / (df['dwt'] + 1)) * (df['speed'] / (df['ship_age'] + 1))
        
        return df
    
    def preprocess_data(self, df):
        """数据预处理"""
        if not self.is_loaded:
            raise ValueError("模型未加载，请先调用load_models()")
        
        # 特征工程
        df_processed = self.revolutionary_feature_engineering(df)
        
        # 获取模型需要的特征
        feature_columns = self.feature_info['feature_columns']
        
        # 确保所有特征都存在
        for col in feature_columns:
            if col not in df_processed.columns:
                df_processed[col] = 0  # 缺失特征用0填充
        
        # 选择特征
        X = df_processed[feature_columns]
        
        # 处理缺失值
        for col in feature_columns:
            if col in self.feature_medians:
                X[col] = X[col].fillna(self.feature_medians[col])
            else:
                X[col] = X[col].fillna(0)
        
        return X
    
    def predict(self, df):
        """预测船舶价格"""
        if not self.is_loaded:
            raise ValueError("模型未加载，请先调用load_models()")
        
        print(f"🔮 预测 {len(df)} 艘船舶的价格...")
        
        # 数据预处理
        X = self.preprocess_data(df)
        
        # 模型预测 (对数空间)
        pred_xgb_log = self.xgb_model.predict(X)
        pred_lgb_log = self.lgb_model.predict(X)
        pred_cat_log = self.cat_model.predict(X)
        
        # 反对数变换
        pred_xgb = np.expm1(pred_xgb_log)
        pred_lgb = np.expm1(pred_lgb_log)
        pred_cat = np.expm1(pred_cat_log)
        
        # 确保预测值为正
        pred_xgb = np.maximum(pred_xgb, 0.1)
        pred_lgb = np.maximum(pred_lgb, 0.1)
        pred_cat = np.maximum(pred_cat, 0.1)
        
        # 加权集成
        weights = list(self.feature_info['model_weights'].values())
        final_predictions = np.average([pred_xgb, pred_lgb, pred_cat], 
                                     weights=weights, axis=0)
        
        print(f"✅ 预测完成!")
        print(f"📊 预测价格范围: {final_predictions.min():.2f} - {final_predictions.max():.2f} 百万美元")
        
        return final_predictions
    
    def predict_single(self, ship_data):
        """预测单艘船舶价格"""
        if isinstance(ship_data, dict):
            df = pd.DataFrame([ship_data])
        else:
            df = pd.DataFrame([ship_data])
        
        predictions = self.predict(df)
        return predictions[0]
    
    def get_model_info(self):
        """获取模型信息"""
        if not self.is_loaded:
            return "模型未加载"
        
        info = f"""
86分船舶估值模型信息:
========================
特征数量: {self.feature_info['feature_count']}
训练样本: {self.feature_info['training_samples']}
目标变换: {self.feature_info['target_transform']}

模型性能:
- XGBoost: {self.feature_info['validation_scores']['xgb_revolutionary']:.4f}
- LightGBM: {self.feature_info['validation_scores']['lgb_revolutionary']:.4f}
- CatBoost: {self.feature_info['validation_scores']['cat_revolutionary']:.4f}

模型权重:
- XGBoost: {self.feature_info['model_weights']['xgb_revolutionary']:.3f}
- LightGBM: {self.feature_info['model_weights']['lgb_revolutionary']:.3f}
- CatBoost: {self.feature_info['model_weights']['cat_revolutionary']:.3f}
        """
        return info


# 使用示例
if __name__ == "__main__":
    # 创建预测器
    predictor = ShipValuationPredictor()
    
    # 加载模型
    predictor.load_models()

    # 显示模型信息
    print(predictor.get_model_info())

    # 示例预测数据
    sample_data = {
        'shipno': 12345,
        'sale_date': '2024-06-15',
        'dwt': 75000,
        'gt': 45000,
        'nt': 35000,
        'built_date': 2015,
        'length': 180,
        'width': 32,
        'draught': 12,
        'speed': 14,
        'totalhorsepowerofmainengines': 8000,
        'totalkilowattsofmainengines': 6000,
        'grain_capacity': 85000,
        'comfactor': 1.2,
        'dryfactor': 1.1
    }

    # 预测单艘船舶
    try:
        price = predictor.predict_single(sample_data)
        print(f"\n🚢 预测价格: {price:.2f} 百万美元")
    except Exception as e:
        print(f"预测失败: {e}")
        print("请确保模型文件存在于 saved_models/ 目录中")
